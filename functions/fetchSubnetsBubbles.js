// Global variable to track which bubble currently has a detail card open
let currentOpenSubnetCardBubbleId = null;

function fetchSubnetsBubbles() {
    const svg = d3.select("#map");
    const container = d3.select("#canvas-container");

    // Detect mobile
    function isMobile() {
        return window.matchMedia("(max-width: 768px)").matches;
    }

    // Dynamic dimensions
    function updateDimensions() {
        const width = container.node().getBoundingClientRect().width || 1200;
        const height = container.node().getBoundingClientRect().height || 900;
        svg.attr("width", width).attr("height", height);
        return { width, height };
    }

    let dimensions = updateDimensions();

    // Adjusted sizes based on mobile
    const baseHostRadius = isMobile() ? 30 : 70;

    // Construct the endpoint URL based on pageType
    const urlParams = new URLSearchParams(window.location.search);
    const pageType = urlParams.has('infra') ? 'subnets' : 'infra';

    let apiUrl;
    if (pageType === 'subnets') {
        apiUrl = `get_subnet_name.php?infra=${encodeURIComponent(urlParams.get('infra'))}`;
    } else {
        console.error('Unsupported pageType:', pageType);
        return;
    }

    // Process status data for the host/service count display
    function processStatusData(data) {
        if (!data || !data.hostCounts || !data.serviceCounts) {
            console.error('Invalid status data format:', data);
            return;
        }

        // Format the data for updateHostAndServiceCounts
        const formattedData = {
            data: {
                count: {
                    // Host counts
                    up: parseInt(data.hostCounts.up) || 0,
                    down: parseInt(data.hostCounts.down) || 0,
                    unreachable: parseInt(data.hostCounts.unreachable) || 0,
                    pending: parseInt(data.hostCounts.pending) || 0,
                    
                    // Service counts
                    ok: parseInt(data.serviceCounts.ok) || 0,
                    warning: parseInt(data.serviceCounts.warning) || 0,
                    critical: parseInt(data.serviceCounts.critical) || 0,
                    unknown: parseInt(data.serviceCounts.unknown) || 0
                }
            }
        };

        // Update the service count too
        formattedData.data.serviceCount = {
            ok: parseInt(data.serviceCounts.ok) || 0,
            warning: parseInt(data.serviceCounts.warning) || 0,
            critical: parseInt(data.serviceCounts.critical) || 0,
            unknown: parseInt(data.serviceCounts.unknown) || 0,
            pending: parseInt(data.serviceCounts.pending) || 0
        };

        // Directly update the displays
        if (typeof updateHostCountDisplay === 'function') {
            updateHostCountDisplay(formattedData);
        }
        
        if (typeof updateServiceCountDisplay === 'function') {
            updateServiceCountDisplay({ data: { count: formattedData.data.serviceCount } });
        }
    }

    // Fetch status counts for infra
    function fetchStatusCounts() {
        const infra = urlParams.get('infra');
        fetch(`get_host_status.php?infra=${encodeURIComponent(infra)}&mode=subnet_counts`)
            .then(response => response.json())
            .then(data => {
                // Process the received data to match the format expected by updateHostAndServiceCounts
                processStatusData(data);
                
                // Force UI update for the counts
                if (typeof updateHostCountDisplay === 'function' && 
                    typeof updateServiceCountDisplay === 'function') {
                    // Format the data for display
                    const formattedData = formatCountDataForDisplay(data);
                    
                    // Update the host and service displays
                    updateHostCountDisplay(formattedData);
                    updateServiceCountDisplay({ 
                        data: { 
                            count: formattedData.data.serviceCount 
                        } 
                    });
                }
            })
            .catch(error => console.error('Error fetching status data:', error));
    }

    // Helper function to format count data for display
    function formatCountDataForDisplay(data) {
        if (!data || !data.hostCounts || !data.serviceCounts) {
            console.error('Invalid status data format:', data);
            return { data: { count: {} } };
        }

        // Format the data for updateHostAndServiceCounts
        const formattedData = {
            data: {
                count: {
                    // Host counts
                    up: parseInt(data.hostCounts.up) || 0,
                    down: parseInt(data.hostCounts.down) || 0,
                    unreachable: parseInt(data.hostCounts.unreachable) || 0,
                    pending: parseInt(data.hostCounts.pending) || 0,
                    
                    // Include service counts to ensure all needed data is present
                    ok: parseInt(data.serviceCounts.ok) || 0,
                    warning: parseInt(data.serviceCounts.warning) || 0,
                    critical: parseInt(data.serviceCounts.critical) || 0,
                    unknown: parseInt(data.serviceCounts.unknown) || 0
                }
            }
        };

        // Set up service count data structure
        formattedData.data.serviceCount = {
            ok: parseInt(data.serviceCounts.ok) || 0,
            warning: parseInt(data.serviceCounts.warning) || 0,
            critical: parseInt(data.serviceCounts.critical) || 0,
            unknown: parseInt(data.serviceCounts.unknown) || 0,
            pending: parseInt(data.serviceCounts.pending) || 0
        };

        return formattedData;
    }

    // Fetch data
    fetch(apiUrl)
        .then(response => {
            return response.json(); // JSON response for subnets
        })
        .then(data => {
            let bubbleData = [];
            let g = svg.append("g");
            let simulation;

            // Fetch status counts for the infra
            fetchStatusCounts();

            // Create separate groups for arrows, bubbles, speed labels to control z-order
            const arrowGroup = g.append("g").attr("class", "arrow-group"); // Arrows go in this group (under)
            const speedLabelGroup = g.append("g").attr("class", "speed-label-group"); // Speed labels go in this group (above bubbles)

            // Zoom behavior with touch support (same as hosts view)
            const zoom = d3.zoom()
                .scaleExtent([0.1, 5])
                .on("zoom", (event) => {
                    g.attr("transform", event.transform);
                });
            
            // Apply zoom behavior without restricting touch events
            svg.call(zoom);
            
            // Enable canvas panning by adding touch listeners to the SVG
            svg.style("touch-action", "none");
            
            // Explicit touch event handling for mobile
            let touchStartX, touchStartY;
            let lastTouchDistance = 0;
            let currentTransform = d3.zoomIdentity;
            
            svg.on("touchstart", function(event) {
                // Store the current transform
                currentTransform = d3.zoomTransform(svg.node());
                
                // Get touch coordinates
                const touches = event.touches;
                if (touches.length === 1) {
                    touchStartX = touches[0].clientX;
                    touchStartY = touches[0].clientY;
                } else if (touches.length === 2) {
                    // For pinch-zoom, calculate initial distance
                    lastTouchDistance = Math.hypot(
                        touches[1].clientX - touches[0].clientX,
                        touches[1].clientY - touches[0].clientY
                    );
                }
                event.preventDefault();
            });
            
            svg.on("touchmove", function(event) {
                const touches = event.touches;
                
                if (touches.length === 1) {
                    // Single touch for panning
                    const dx = touches[0].clientX - touchStartX;
                    const dy = touches[0].clientY - touchStartY;
                    
                    // Apply translation based on touch movement
                    const newTransform = currentTransform.translate(dx / currentTransform.k, dy / currentTransform.k);
                    svg.call(zoom.transform, newTransform);
                } else if (touches.length === 2) {
                    // Two touches for pinch zoom
                    const currentDistance = Math.hypot(
                        touches[1].clientX - touches[0].clientX,
                        touches[1].clientY - touches[0].clientY
                    );
                    
                    if (lastTouchDistance > 0) {
                        // Calculate zoom factor
                        const scaleFactor = currentDistance / lastTouchDistance;
                        
                        // Get the midpoint of the two touches
                        const midX = (touches[0].clientX + touches[1].clientX) / 2;
                        const midY = (touches[0].clientY + touches[1].clientY) / 2;
                        
                        // Convert midpoint to SVG coordinates
                        const svgPoint = svg.node().createSVGPoint();
                        svgPoint.x = midX;
                        svgPoint.y = midY;
                        
                        // Apply zoom centered on the midpoint
                        const newK = currentTransform.k * scaleFactor;
                        const constrainedK = Math.max(0.1, Math.min(5, newK)); // Respect zoom limits
                        
                        // Create transform with new scale
                        const newTransform = currentTransform.scale(constrainedK / currentTransform.k);
                        svg.call(zoom.transform, newTransform);
                        
                        // Update for next move
                        currentTransform = newTransform;
                        lastTouchDistance = currentDistance;
                    }
                }
                event.preventDefault();
            });
            
            svg.on("touchend", function() {
                // Reset touch states when touches end
                lastTouchDistance = 0;
            });
            
            // Process data based on pageType
            const centerX = dimensions.width / 2;
            const centerY = dimensions.height / 2;
            
            const subnets = data.data || [];
            bubbleData = subnets.map(subnet => ({
                id: subnet.id,
                hostname: subnet.subnetNickname || subnet.subnet,
                ip: subnet.subnet,
                subnet: subnet.subnet,
                infra: subnet.infra,
                x: centerX + (Math.random() - 0.5) * dimensions.width * 0.5, // Random position within 50% of width
                y: centerY + (Math.random() - 0.5) * dimensions.height * 0.5, // Random position within 50% of height
                size: baseHostRadius,
                status: 'pending', // Default status, will be updated later
                problemHosts: 0 // Default count, will be updated later
            }));

            // Fetch the status data for each subnet
            fetchSubnetStatus(bubbleData).then(updatedBubbleData => {
                // Update bubbleData with status information
                bubbleData = updatedBubbleData;
                
                // Fetch and render subnet relationships
                fetchSubnetRelationships(bubbleData).then(relationshipMap => {
                    renderSubnetArrows(relationshipMap);
                });
                
                // Start or restart simulation with updated data
                if (simulation) {
                    simulation.nodes(bubbleData).alpha(1).restart();
                }
            });

            simulation = d3.forceSimulation(bubbleData)
                .force("charge", d3.forceManyBody().strength(isMobile() ? -10 : -30)) // Stronger repulsion
                .force("collision", d3.forceCollide().radius(d => d.size + 2).strength(0.8))
                .force("center", d3.forceCenter(centerX, centerY).strength(0.05)) // Gentle centering force
                .alphaDecay(0.02) // Slightly faster stabilization
                .on("tick", ticked);

            // Host bubbles
            const hostBubbles = g.selectAll(".host-bubble")
                .data(bubbleData)
                .enter()
                .append("circle")
                .attr("class", d => `host-bubble ${d.status}`)
                .attr("data-hostgroup", d => d.hostname) 
                .attr("r", d => d.size)
                .attr("cx", d => d.x)
                .attr("cy", d => d.y)
                .call(drag(simulation))
                .on("touchstart", function(event) {
                    // Prevent default only for drag operations
                    event.stopPropagation();
                })
                .on("mouseover", function () {
                    d3.select(this).transition().duration(200).attr("r", d => d.size * 1.2);
                })
                .on("mouseout", function () {
                    d3.select(this).transition().duration(200).attr("r", d => d.size);
                })
                .on("click", function (event, d) {
                    // Check if a detail card is already open for this bubble
                    if (currentOpenSubnetCardBubbleId === d.id) {
                        // Navigate directly to the subnet hosts view
                        window.location.href = `hosts.php?subnet=${encodeURIComponent(d.subnet)}&subnetNickname=${encodeURIComponent(d.hostname)}&infra=${encodeURIComponent(d.infra)}&subnet=true`;
                    } else {
                        // Show detail card instead of navigating directly to hosts.php
                        showSubnetDetailCard(event, d);
                        currentOpenSubnetCardBubbleId = d.id;
                    }
                });

            // Host labels
            const hostLabels = g.selectAll(".bubble-text")
                .data(bubbleData)
                .enter()
                .append("text")
                .attr("class", "bubble-text")
                .style("font-size", isMobile() ? "10px" : "12px")
                .attr("x", d => d.x)
                .attr("y", d => d.y + (isMobile() ? 3 : 5))
                .text(d => {
                    const maxLength = d.size * 1.6; // Adjust this factor based on your needs
                    if (d.hostname.length * 6 > maxLength) { // Approximate character width
                        return d.hostname.substring(0, Math.floor(maxLength / 6)) + "...";
                    }
                    return d.hostname;
                });

            // Add arrow marker definition (same as hosts view)
            svg.append("defs").append("marker")
                .attr("id", "arrowhead")
                .attr("viewBox", "0 -5 10 10")
                .attr("refX", 8)
                .attr("refY", 0)
                .attr("markerWidth", 6)
                .attr("markerHeight", 6)
                .attr("orient", "auto")
                .append("path")
                .attr("d", "M0,-5L10,0L0,5")
                .attr("fill", "#4da6ff");

            // Update positions on tick
            function ticked() {
                hostBubbles
                    .attr("cx", d => d.x)
                    .attr("cy", d => d.y);
                hostLabels
                    .attr("x", d => d.x)
                    .attr("y", d => d.y + (isMobile() ? 3 : 5));
                bubbleData.forEach(d => {
                    const badge = g.select(`.badge-${d.id}`);
                    if (!badge.empty()) {
                        badge.attr("transform", `translate(${d.x + d.size * 0.7}, ${d.y - d.size * 0.7})`);
                    }
                });
                
                // Update arrow positions
                updateArrowPositions();
            }

            // Fetch status data for each subnet
            async function fetchSubnetStatus(bubbles) {
                const infra = urlParams.get('infra');
                
                try {
                    // Get status for all subnets in the current infra
                    const response = await fetch(`get_subnet_status.php?infra=${encodeURIComponent(infra)}`);
                    const data = await response.json();
                    
                    // Update each bubble with its status and problem host count
                    return bubbles.map(bubble => {
                        const subnet = bubble.subnet;
                        const subnetData = data.find(s => s.subnet === subnet);
                        
                        if (subnetData) {
                            // Update bubble with status information
                            bubble.status = subnetData.worstStatus || 'ok';
                            bubble.problemHosts = subnetData.problemHosts || 0;
                            
                            // Store service statuses and host status for filtering
                            bubble.serviceStatuses = subnetData.serviceStatuses || {};
                            bubble.hostStatus = subnetData.hostStatus || 2; // Default to OK (2)
                            
                            // Store which host statuses are present in this subnet
                            bubble.hostStatusPresent = subnetData.hostStatusPresent || {};
                            bubble.hostStatuses = subnetData.hostStatuses || [];
                            
                            // Store status counts from the response
                            bubble.statusCounts = subnetData.statusCounts || {
                                host_status: { up: 0, down: 0, unreachable: 0, pending: 0 },
                                service_status: { ok: 0, warning: 0, critical: 0, unknown: 0, pending: 0 }
                            };
                            
                            // Make sure statusCounts has all required properties with default values
                            if (!bubble.statusCounts.host_status) {
                                bubble.statusCounts.host_status = { up: 0, down: 0, unreachable: 0, pending: 0 };
                            }
                            if (!bubble.statusCounts.service_status) {
                                bubble.statusCounts.service_status = { ok: 0, warning: 0, critical: 0, unknown: 0, pending: 0 };
                            }
                            
                            // Update bubble class to reflect status
                            const hostBubble = hostBubbles
                                .filter(d => d.id === bubble.id);
                                
                            hostBubble
                                .attr("class", `host-bubble ${bubble.status}`)
                                .attr("data-host-status", bubble.hostStatus)
                                .attr("data-service-statuses", JSON.stringify(bubble.serviceStatuses))
                                .attr("data-host-statuses-present", JSON.stringify(bubble.hostStatusPresent));
                            
                            // Add or update badge
                            if (bubble.problemHosts > 0) {
                                createOrUpdateBadge(g, bubble);
                            }
                        }
                        
                        return bubble;
                    });
                } catch (error) {
                    console.error("Error fetching subnet status:", error);
                    return bubbles; // Return original data if fetch fails
                }
            }

            // Create or update badge for a bubble
            function createOrUpdateBadge(container, bubble) {
                if (bubble.problemHosts <= 0) return;
                
                let badge = container.select(`.badge-${bubble.id}`);
                
                if (badge.empty()) {
                    badge = container
                        .append("g")
                        .attr("class", `badge badge-${bubble.id}`)
                        .attr("transform", `translate(${bubble.x + bubble.size * 0.7}, ${bubble.y - bubble.size * 0.7})`);
                    
                    // Add the badge circle
                    badge.append("circle")
                        .attr("r", 10)
                        .style("fill", "#ff4444")
                        .style("stroke", "#fff")
                        .style("stroke-width", "1px");
                    
                    // Add the badge text
                    badge.append("text")
                        .attr("text-anchor", "middle")
                        .attr("dy", ".35em")
                        .style("font-size", "12px")
                        .style("font-weight", "bold")
                        .style("fill", "#fff");
                }
                
                // Update badge text to show problem host count
                badge.select("text").text(bubble.problemHosts);
                
                // Position badge
                badge.attr("transform", `translate(${bubble.x + bubble.size * 0.7}, ${bubble.y - bubble.size * 0.7})`);
            }

            // Fetch subnet relationships from the database
            async function fetchSubnetRelationships(subnetData) {
                const relationshipMap = new Map();
                try {
                    const response = await fetch('src/subnetRelationship/getSubnetRelationships.php');
                    if (!response.ok) {
                        console.error("Failed to fetch subnet relationships from DB:", response.statusText);
                        return relationshipMap;
                    }
                    const dbRelationships = await response.json();

                    // Group children by parent subnet with additional metadata
                    const childrenByParentSubnet = new Map();
                    for (const rel of dbRelationships) {
                        if (!childrenByParentSubnet.has(rel.parent_subnet)) {
                            childrenByParentSubnet.set(rel.parent_subnet, []);
                        }
                        childrenByParentSubnet.get(rel.parent_subnet).push({
                            childSubnet: rel.child_subnet,
                            source: rel.source,
                            connectionType: rel.connection_type,
                            parentSubnetNickname: rel.parent_subnet_nickname,
                            childSubnetNickname: rel.child_subnet_nickname,
                            hostCount: rel.host_count
                        });
                    }

                    // Create the relationshipMap expected by renderSubnetArrows
                    childrenByParentSubnet.forEach((childrenData, parentSubnet) => {
                        const parentSubnetBubble = subnetData.find(s => s.subnet === parentSubnet);
                        if (parentSubnetBubble) {
                            // Find all child subnets that exist in the current view
                            const visibleChildren = childrenData.filter(childData =>
                                subnetData.some(s => s.subnet === childData.childSubnet)
                            );

                            if (visibleChildren.length > 0) {
                                relationshipMap.set(parentSubnetBubble.id, {
                                    parent: parentSubnetBubble,
                                    childrenData: visibleChildren
                                });
                            }
                        }
                    });
                } catch (err) {
                    console.error('Error fetching or processing subnet relationships:', err);
                }
                return relationshipMap;
            }

            // Render arrows from parent to children subnets
            function renderSubnetArrows(relationshipMap) {
                // Flatten the parent-child relationships into individual pairs
                const arrowData = [];
                relationshipMap.forEach((value, parentId) => {
                    const { parent, childrenData = [] } = value || {};

                    if (childrenData && childrenData.length > 0) {
                        childrenData.forEach(childData => {
                            const childSubnet = bubbleData.find(s =>
                                s.subnet === childData.childSubnet
                            );
                            if (childSubnet) {
                                arrowData.push({ 
                                    parentId, 
                                    parent, 
                                    childSubnet: childData.childSubnet,
                                    childSubnetBubble: childSubnet,
                                    source: childData.source,
                                    connectionType: childData.connectionType,
                                    parentSubnetNickname: childData.parentSubnetNickname,
                                    childSubnetNickname: childData.childSubnetNickname,
                                    hostCount: childData.hostCount
                                });
                            } else {
                                console.warn(`Child subnet ${childData.childSubnet} not found in subnetData`);
                            }
                        });
                    }
                });

                // Remove existing arrows and speed labels
                arrowGroup.selectAll(".subnet-connection-arrow").remove();
                speedLabelGroup.selectAll(".subnet-speed-label").remove();

                // Draw arrows with exact same styling as hosts view
                const arrows = arrowGroup.selectAll(".subnet-connection-arrow")
                    .data(arrowData)
                    .enter()
                    .append("path")
                    .attr("class", "subnet-connection-arrow parent-child-arrow")
                    .attr("fill", "none")
                    .attr("stroke-linecap", "round")
                    .style("pointer-events", "none");

                // Add speed labels alongside arrows (same styling as hosts view)
                const speedLabels = speedLabelGroup.selectAll(".subnet-speed-label")
                    .data(arrowData)
                    .enter()
                    .append("text")
                    .attr("class", "subnet-speed-label speed-label")
                    .style("pointer-events", "all")
                    .style("cursor", "pointer")
                    .style("text-anchor", "middle")
                    .style("dominant-baseline", "middle")
                    .style("display", "block")
                    .text(d => `${d.hostCount} connections`)
                    .on("mouseenter", function(event, d) {
                        event.stopPropagation();
                        showSubnetConnectionTooltip(event, d);
                    })
                    .on("mouseleave", function(event) {
                        event.stopPropagation();
                        hideSubnetConnectionTooltip();
                    });

                // Store arrow data for position updates
                window.subnetArrowData = arrowData;
            }

            // Update arrow positions based on current bubble positions (same logic as hosts view)
            function updateArrowPositions() {
                if (!window.subnetArrowData) return;

                arrowGroup.selectAll(".subnet-connection-arrow")
                    .attr("d", d => {
                        const parent = d.parent;
                        const child = d.childSubnetBubble;
                        
                        if (!parent || !child) return "";
                        
                        const dx = child.x - parent.x; // Direction from parent to child
                        const dy = child.y - parent.y;
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        const parentPadding = parent.size + 5;
                        const childPadding = child.size + 5;

                        if (distance < parentPadding + childPadding) return "";

                        const angle = Math.atan2(dy, dx);
                        const startX = parent.x + Math.cos(angle) * parentPadding; // Start near parent
                        const startY = parent.y + Math.sin(angle) * parentPadding;
                        const endX = child.x - Math.cos(angle) * childPadding; // End near child
                        const endY = child.y - Math.sin(angle) * childPadding;

                        return `M${startX},${startY}L${endX},${endY}`;
                    });

                speedLabelGroup.selectAll(".subnet-speed-label")
                    .attr("x", d => {
                        const parent = d.parent;
                        const child = d.childSubnetBubble;
                        if (!parent || !child) return 0;

                        const dx = child.x - parent.x;
                        const dy = child.y - parent.y;
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        const parentPadding = parent.size + 5;
                        const childPadding = child.size + 5;

                        if (distance < parentPadding + childPadding) return 0;

                        const angle = Math.atan2(dy, dx);
                        const midPoint = (distance - parentPadding - childPadding) / 2 + parentPadding;
                        
                        // Offset perpendicular to the arrow direction to avoid overlap
                        const perpendicularAngle = angle + Math.PI / 2; // 90 degrees perpendicular
                        const offsetDistance = 15; // Distance to offset the label
                        
                        const baseX = parent.x + Math.cos(angle) * midPoint;
                        const baseY = parent.y + Math.sin(angle) * midPoint;
                        
                        return baseX + Math.cos(perpendicularAngle) * offsetDistance;
                    })
                    .attr("y", d => {
                        const parent = d.parent;
                        const child = d.childSubnetBubble;
                        if (!parent || !child) return 0;

                        const dx = child.x - parent.x;
                        const dy = child.y - parent.y;
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        const parentPadding = parent.size + 5;
                        const childPadding = child.size + 5;

                        if (distance < parentPadding + childPadding) return 0;

                        const angle = Math.atan2(dy, dx);
                        const midPoint = (distance - parentPadding - childPadding) / 2 + parentPadding;
                        
                        // Offset perpendicular to the arrow direction to avoid overlap
                        const perpendicularAngle = angle + Math.PI / 2; // 90 degrees perpendicular
                        const offsetDistance = 15; // Distance to offset the label
                        
                        const baseX = parent.x + Math.cos(angle) * midPoint;
                        const baseY = parent.y + Math.sin(angle) * midPoint;
                        
                        return baseY + Math.sin(perpendicularAngle) * offsetDistance;
                    })
                    .style("display", d => {
                        const parent = d.parent;
                        const child = d.childSubnetBubble;
                        if (!parent || !child) return "none";

                        const dx = child.x - parent.x;
                        const dy = child.y - parent.y;
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        const parentPadding = parent.size + 5;
                        const childPadding = child.size + 5;

                        return distance < parentPadding + childPadding ? "none" : "block";
                    });
            }

            // Show tooltip for subnet connections (same styling as hosts view)
            function showSubnetConnectionTooltip(event, arrowData) {
                // Determine source display text (same logic as hosts view)
                let sourceText = 'Unknown';
                if (arrowData.source === 'netdisco') {
                    sourceText = 'SPM (Auto-detected)';
                } else if (arrowData.source === 'nagiosql') {
                    sourceText = 'APM';
                } else if (arrowData.source === 'both') {
                    sourceText = 'Both (SPM + APM)';
                }
                
                // Determine type display text
                let typeText = arrowData.connectionType || 'Unknown';
                if (arrowData.source === 'nagiosql') {
                    typeText = 'Manual';
                }
                
                const tooltipContent = `
                    <div class="speed-tooltip">
                        <div class="tooltip-header">Subnet Connection Details</div>
                        <div class="tooltip-row">
                            <span class="tooltip-label">From:</span>
                            <span class="tooltip-value">${arrowData.childSubnetNickname}</span>
                        </div>
                        <div class="tooltip-separator"></div>
                        <div class="tooltip-row">
                            <span class="tooltip-label">To:</span>
                            <span class="tooltip-value">${arrowData.parentSubnetNickname}</span>
                        </div>
                        <div class="tooltip-separator"></div>
                        <div class="tooltip-row">
                            <span class="tooltip-label">Connections:</span>
                            <span class="tooltip-value">${arrowData.hostCount}</span>
                        </div>
                        <div class="tooltip-separator"></div>
                        <div class="tooltip-row">
                            <span class="tooltip-label">Type:</span>
                            <span class="tooltip-value">${typeText}</span>
                        </div>
                        <div class="tooltip-separator"></div>
                        <div class="tooltip-row">
                            <span class="tooltip-label">Source:</span>
                            <span class="tooltip-value">${sourceText}</span>
                        </div>
                    </div>
                `;

                // Create tooltip element (same as hosts view)
                let tooltip = d3.select("body").select(".speed-tooltip-container");
                if (tooltip.empty()) {
                    tooltip = d3.select("body").append("div")
                        .attr("class", "speed-tooltip-container")
                        .style("position", "absolute")
                        .style("z-index", "10000")
                        .style("display", "none");
                }

                tooltip.html(tooltipContent);
                positionTooltip(event, tooltip);
            }

            // Hide subnet connection tooltip (same as hosts view)
            function hideSubnetConnectionTooltip() {
                const tooltip = d3.select("body").select(".speed-tooltip-container");
                if (!tooltip.empty()) {
                    tooltip.style("display", "none");
                }
            }

            // Helper function to position tooltip within viewport bounds
            function positionTooltip(event, tooltip) {
                // Show the tooltip temporarily to get accurate dimensions
                tooltip.style("display", "block");

                // Get tooltip dimensions with fallbacks
                const tooltipHeight = tooltip.node().offsetHeight || 200;
                const tooltipWidth = tooltip.node().offsetWidth || 250;
                const viewportHeight = window.innerHeight;
                const viewportWidth = window.innerWidth;
                const buffer = 10; // Small buffer from viewport edges

                let top = event.pageY - 10;
                let left = event.pageX + 10;

                // Adjust if tooltip would go beyond bottom edge
                if (top + tooltipHeight + buffer > viewportHeight) {
                    top = event.pageY - tooltipHeight - 10; // Flip above the mouse
                }
                
                // Adjust if tooltip would go beyond right edge
                if (left + tooltipWidth + buffer > viewportWidth) {
                    left = event.pageX - tooltipWidth - 10; // Flip left of the mouse
                }

                // Ensure it doesn't go off the top or left edge
                top = Math.max(buffer, top); // Keep a small buffer from top
                left = Math.max(buffer, left); // Keep a small buffer from left

                tooltip.style("top", `${top}px`)
                       .style("left", `${left}px`);
            }

            // Drag behavior
            function drag(sim) {
                function dragstarted(event, d) {
                    if (!event.active) sim.alphaTarget(0.3).restart();
                    d.fx = d.x;
                    d.fy = d.y;
                }

                function dragged(event, d) {
                    d.fx = event.x;
                    d.fy = event.y;
                }

                function dragended(event, d) {
                    if (!event.active) sim.alphaTarget(0);
                    d.fx = null;
                    d.fy = null;
                }

                return d3.drag()
                    .on("start", dragstarted)
                    .on("drag", dragged)
                    .on("end", dragended)
                    .touchable(true);
            }

            // Reset zoom on double-click
            svg.on("dblclick.zoom", null);
            svg.on("dblclick", () => {
                svg.transition()
                    .duration(750)
                    .call(zoom.transform, d3.zoomIdentity);
            });

            // Handle resize dynamically
            window.addEventListener("resize", () => {
                dimensions = updateDimensions();
                simulation.nodes(bubbleData).alpha(1).restart();
            });

            // Animate bubbles
            function animateBubbles() {
                hostBubbles.each(function (d) {
                    d3.select(this)
                        .transition()
                        .duration(2000 + Math.random() * 1000)
                        .ease(d3.easeSinInOut)
                        .attr("r", d => d.size * 1.1)
                        .transition()
                        .duration(2000 + Math.random() * 1000)
                        .ease(d3.easeSinInOut)
                        .attr("r", d => d.size)
                        .on("end", animateBubbles);
                });
            }
            animateBubbles();

            // Set up periodic status updates
            setInterval(() => {
                fetchSubnetStatus(bubbleData).then(updatedBubbleData => {
                    bubbleData = updatedBubbleData;
                });
                
                // Also refresh the status counts
                fetchStatusCounts();
            }, 10000); // Update every 10 seconds
        })
        .catch(error => console.error('Error fetching data:', error));

    // Function to show subnet detail card
    function showSubnetDetailCard(event, d) {
        // Prevent event from bubbling up to canvas
        event.stopPropagation();
        
            // Check if a card already exists and remove it
    const existingCard = document.getElementById('subnet-detail-card');
    if (existingCard) {
        existingCard.remove();
    }
    
    // Update the global tracking variable
    currentOpenSubnetCardBubbleId = d.id;
        
        // Create the detail card element
        const detailCard = document.createElement('div');
        detailCard.id = 'subnet-detail-card';
        detailCard.className = 'subnet-detail-card';
        
        // Get the bubble's position relative to the viewport
        const bubbleBoundingRect = event.target.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        // Define card dimensions with some margin for safety
        const cardWidth = 350;
        const cardHeight = 450;
        const margin = 10;
        
        // Calculate optimal position
        // Start by trying to position to the right of the bubble
        let cardLeft = bubbleBoundingRect.right + 20;
        
        // If too close to right edge, try left side
        if (cardLeft + cardWidth > viewportWidth - margin) {
            cardLeft = bubbleBoundingRect.left - cardWidth - 20;
        }
        
        // If still doesn't fit (bubble is too wide or viewport too narrow), center horizontally
        if (cardLeft < margin || cardLeft + cardWidth > viewportWidth - margin) {
            cardLeft = Math.max(margin, Math.min(viewportWidth - cardWidth - margin, 
                               (viewportWidth - cardWidth) / 2));
        }
        
        // For vertical positioning, try to align with bubble center
        let cardTop = bubbleBoundingRect.top + (bubbleBoundingRect.height / 2) - (cardHeight / 2);
        
        // Ensure it doesn't go above or below viewport
        if (cardTop < margin) {
            cardTop = margin;
        } else if (cardTop + cardHeight > viewportHeight - margin) {
            cardTop = viewportHeight - cardHeight - margin;
        }
        
        // Set the card position
        detailCard.style.left = `${cardLeft}px`;
        detailCard.style.top = `${cardTop}px`;
        
        // Format the status counts for display
        const hostCounts = d.hostStatusPresent ? 
            `<div class="status-count-section">
                <div class="status-count-title">Host Status</div>
                <div class="status-count-row">
                    ${d.hostStatusPresent.up ? 
                        `<span class="host-count ok" title="Up">Up: ${d.statusCounts && d.statusCounts.host_status ? d.statusCounts.host_status.up : 0}</span>` 
                        : ''}
                    ${d.hostStatusPresent.down ? 
                        `<span class="host-count down" title="Down">Down: ${d.statusCounts && d.statusCounts.host_status ? d.statusCounts.host_status.down : 0}</span>` 
                        : ''}
                    ${d.hostStatusPresent.unreachable ? 
                        `<span class="host-count unreachable" title="Unreachable">Unreachable: ${d.statusCounts && d.statusCounts.host_status ? d.statusCounts.host_status.unreachable : 0}</span>` 
                        : ''}
                    ${d.hostStatusPresent.pending ? 
                        `<span class="host-count pending" title="Pending">Pending: ${d.statusCounts && d.statusCounts.host_status ? d.statusCounts.host_status.pending : 0}</span>` 
                        : ''}
                </div>
            </div>` 
            : '';
        
        // Service count section
        const hasNonZeroServiceCount = d.statusCounts && d.statusCounts.service_status && 
            (d.statusCounts.service_status.ok > 0 || 
            d.statusCounts.service_status.warning > 0 || 
            d.statusCounts.service_status.critical > 0 || 
            d.statusCounts.service_status.unknown > 0 || 
            d.statusCounts.service_status.pending > 0);

        const serviceCounts = hasNonZeroServiceCount ? 
            `<div class="status-count-section">
                <div class="status-count-title">Service Status</div>
                <div class="status-count-row">
                    ${d.statusCounts.service_status.ok > 0 ? 
                        `<span class="service-count ok" title="OK">OK: ${d.statusCounts.service_status.ok}</span>` : ''}
                    ${d.statusCounts.service_status.warning > 0 ? 
                        `<span class="service-count warning" title="Warning">Warning: ${d.statusCounts.service_status.warning}</span>` : ''}
                    ${d.statusCounts.service_status.critical > 0 ? 
                        `<span class="service-count critical" title="Critical">Critical: ${d.statusCounts.service_status.critical}</span>` : ''}
                    ${d.statusCounts.service_status.unknown > 0 ? 
                        `<span class="service-count unknown" title="Unknown">Unknown: ${d.statusCounts.service_status.unknown}</span>` : ''}
                    ${d.statusCounts.service_status.pending > 0 ? 
                        `<span class="service-count pending" title="Pending">Pending: ${d.statusCounts.service_status.pending}</span>` : ''}
                </div>
            </div>`
            : '';
        
        // Calculate total hosts and services directly from status counts
        const totalHosts = 
            (d.statusCounts && d.statusCounts.host_status) ? 
            (d.statusCounts.host_status.up || 0) + 
            (d.statusCounts.host_status.down || 0) + 
            (d.statusCounts.host_status.unreachable || 0) + 
            (d.statusCounts.host_status.pending || 0) : 0;
        
        const totalServices = 
            (d.statusCounts && d.statusCounts.service_status) ? 
            (d.statusCounts.service_status.ok || 0) + 
            (d.statusCounts.service_status.warning || 0) + 
            (d.statusCounts.service_status.critical || 0) + 
            (d.statusCounts.service_status.unknown || 0) + 
            (d.statusCounts.service_status.pending || 0) : 0;
        
        // Prepare status display text - add "Worst:" prefix for non-OK statuses
        const statusDisplayText = d.status === 'ok' ? 'OK' : `WORST: ${d.status.toUpperCase()}`;
        
        // Set the card content
        detailCard.innerHTML = `
            <div class="subnet-card-header ${d.status}">
                <span class="subnet-card-close">&times;</span>
                <h3 class="subnet-card-title">${d.hostname}</h3>
                <div class="subnet-card-subtitle">${d.subnet}</div>
            </div>
            <div class="subnet-card-body">
                <div class="subnet-card-info">
                    <div class="subnet-status-badge ${d.status}">TOTAL</div>
                    <div class="subnet-counts">
                        <div class="count-item">
                            <span class="count-value">${totalHosts}</span>
                            <span class="count-label">Hosts</span>
                        </div>
                        <div class="count-item">
                            <span class="count-value">${totalServices}</span>
                            <span class="count-label">Services</span>
                        </div>
                    </div>
                </div>
                
                ${hostCounts}
                ${serviceCounts}
                
                <div class="subnet-card-actions">
                    <button class="subnet-card-btn primary" onclick="viewSubnet('${d.subnet}', '${d.hostname}', '${urlParams.get('infra')}')">
                        View Hosts
                    </button>
                </div>
            </div>
        `;
        
        // Add the card to the document
        document.body.appendChild(detailCard);
        
        // Add a resize observer to adjust the card position if its size changes
        // This ensures the card stays within the viewport even after dynamic content renders
        const resizeObserver = new ResizeObserver(entries => {
            for (const entry of entries) {
                const card = entry.target;
                const rect = card.getBoundingClientRect();
                const margin = 10;
                
                // If card extends beyond viewport edges, reposition it
                if (rect.right > window.innerWidth - margin) {
                    card.style.left = `${Math.max(margin, window.innerWidth - rect.width - margin)}px`;
                }
                
                if (rect.bottom > window.innerHeight - margin) {
                    card.style.top = `${Math.max(margin, window.innerHeight - rect.height - margin)}px`;
                }
                
                if (rect.left < margin) {
                    card.style.left = `${margin}px`;
                }
                
                if (rect.top < margin) {
                    card.style.top = `${margin}px`;
                }
            }
        });
        
        // Start observing the detail card
        resizeObserver.observe(detailCard);
        
        // Add card close button event handler
        const closeButton = detailCard.querySelector('.subnet-card-close');
        closeButton.addEventListener('click', function() {
            resizeObserver.disconnect();
            detailCard.remove();
            // Clear the tracking variable when card is closed
            currentOpenSubnetCardBubbleId = null;
        });
        
        // Close the card when clicking outside
        document.addEventListener('click', function closeCardOnOutsideClick(e) {
            // If the click is outside the card and not on a bubble, close the card
            if (!detailCard.contains(e.target) && !e.target.classList.contains('host-bubble')) {
                resizeObserver.disconnect();
                detailCard.remove();
                document.removeEventListener('click', closeCardOnOutsideClick);
                // Clear the tracking variable when card is closed
                currentOpenSubnetCardBubbleId = null;
            }
        });
    }

    // Add function to navigate to subnet hosts page (called from the detail card)
    function viewSubnet(subnet, hostname, infra) {
        window.location.href = `hosts.php?subnet=${encodeURIComponent(subnet)}&subnetNickname=${encodeURIComponent(hostname)}&infra=${encodeURIComponent(infra)}&subnet=true`;
    }
}